const db = require("../config/database");

class Task {


  // 根据任务单号查找任务单（优先使用原表，失败时使用备用表）
  static async findById(taskNumber) {
    try {
      // 首先尝试从原始表获取数据
      const result = await db.execute(
        `SELECT
          xpo.BillNo as id,
          xpo.BillNo as task_number,
          xpo.ProjectId as project_id,
          xpo.BizPartnerId as construction_unit_id,
          CONCAT(
            ISNULL(cmg.MaterialName, ''),
            CASE WHEN cmg.MaterialName IS NOT NULL AND xbi.ImperviousName IS NOT NULL THEN ' ' ELSE '' END,
            ISNULL(xbi.ImperviousName, ''),
            CASE WHEN (cmg.MaterialName IS NOT NULL OR xbi.ImperviousName IS NOT NULL) AND xbf.FolderName IS NOT NULL THEN ' ' ELSE '' END,
            ISNULL(xbf.FolderName, '')
          ) as strength_grade,
          xpo.X_JZPart as part_name,
          CASE
            WHEN xpo.DemandBeginDate IS NOT NULL AND xpo.DemandBeginDate > 0
            THEN CONVERT(DATETIME, CAST(xpo.DemandBeginDate AS VARCHAR(8)), 112)
            ELSE NULL
          END as scheduled_time,
          ISNULL(xpo.X_SupplyState, 0) as supply_status,
          NULL as actual_time,
          NULL as feedback_user,
          1 as status,
          cp.ProjectName as project_name,
          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,
          cp.ProjectId as project_code
         FROM dbo.X_ppProduceOrder xpo
         LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId
         LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId
         LEFT JOIN dbo.comMaterialGroup cmg ON xpo.MaterialId = cmg.MaterialId
         LEFT JOIN dbo.X_BetonImpervious xbi ON xpo.X_ImperviousId = xbi.ImperviousId
         LEFT JOIN dbo.X_BetonFolder xbf ON xpo.X_FolderId = xbf.FolderId
         WHERE xpo.BillNo = ?`,
        [taskNumber]
      );
      return result[0][0];
    } catch (error) {
      console.warn('原始表查询失败，尝试使用备用表:', error.message);

      // 使用备用表
      try {
        const backupResult = await db.execute(
          `SELECT
            task_number,
            project_id,
            construction_unit_id,
            construction_unit,
            strength_grade,
            part_name,
            scheduled_time,
            supply_status,
            actual_time,
            feedback_user,
            status,
            project_name
           FROM dbo.backup_tasks
           WHERE task_number = ?`,
          [taskNumber]
        );
        return backupResult[0][0];
      } catch (backupError) {
        console.error('备用表查询也失败:', backupError.message);
        throw error; // 抛出原始错误
      }
    }
  }

  // 获取公司的所有任务单列表（优先使用原表，失败时使用备用表）
  static async getByCompanyId(companyId, filters = {}) {
    try {
      // 首先尝试从原始表获取数据
      let sql = `
        SELECT
          xpo.BillNo as id,
          xpo.BillNo as task_number,
          xpo.ProjectId as project_id,
          xpo.BizPartnerId as construction_unit_id,
          CONCAT(
            ISNULL(cmg.MaterialName, ''),
            CASE WHEN cmg.MaterialName IS NOT NULL AND xbi.ImperviousName IS NOT NULL THEN ' ' ELSE '' END,
            ISNULL(xbi.ImperviousName, ''),
            CASE WHEN (cmg.MaterialName IS NOT NULL OR xbi.ImperviousName IS NOT NULL) AND xbf.FolderName IS NOT NULL THEN ' ' ELSE '' END,
            ISNULL(xbf.FolderName, '')
          ) as strength_grade,
          xpo.X_JZPart as part_name,
          CASE
            WHEN xpo.DemandBeginDate IS NOT NULL AND xpo.DemandBeginDate > 0
            THEN CONVERT(DATETIME, CAST(xpo.DemandBeginDate AS VARCHAR(8)), 112)
            ELSE NULL
          END as scheduled_time,
          ISNULL(xpo.X_SupplyState, 0) as supply_status,
          NULL as actual_time,
          NULL as feedback_user,
          1 as status,
          cp.ProjectName as project_name,
          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,
          cp.ProjectId as project_code,
          ISNULL(feedback_stats.feedback_count, 0) as feedback_count
        FROM dbo.X_ppProduceOrder xpo
        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId
        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId
        LEFT JOIN dbo.comMaterialGroup cmg ON xpo.MaterialId = cmg.MaterialId
        LEFT JOIN dbo.X_BetonImpervious xbi ON xpo.X_ImperviousId = xbi.ImperviousId
        LEFT JOIN dbo.X_BetonFolder xbf ON xpo.X_FolderId = xbf.FolderId
        LEFT JOIN (
          SELECT
            f.TaskNumber,
            COUNT(DISTINCT f.Id) as feedback_count
          FROM dbo.CU_feedbacks f
          WHERE f.Status = 1
          GROUP BY f.TaskNumber
        ) feedback_stats ON xpo.BillNo = feedback_stats.TaskNumber
        WHERE cp.X_OrgId = ?
      `;
      const params = [companyId];

      // 添加筛选条件
      if (filters.supply_status !== undefined && filters.supply_status !== '') {
        // 支持数字状态值和字符串状态值
        let statusValue = filters.supply_status;
        if (typeof statusValue === 'string') {
          // 将字符串状态转换为数字状态
          const statusMap = {
            'pending': 0,
            'supplying': 1,
            'completed': 2,
            'cancelled': 3
          };
          statusValue = statusMap[statusValue] !== undefined ? statusMap[statusValue] : parseInt(statusValue);
        }
        sql += " AND xpo.X_SupplyState = ?";
        params.push(statusValue);
      }

      if (filters.keyword) {
        sql += " AND (xpo.BillNo LIKE ? OR xpo.X_JZPart LIKE ?)";
        params.push(`%${filters.keyword}%`, `%${filters.keyword}%`);
      }

      if (filters.date_from) {
        sql += " AND xpo.DemandBeginDate >= ?";
        // 将日期转换为YYYYMMDD格式的整数进行比较
        const dateFrom = new Date(filters.date_from);
        const dateFromInt = parseInt(dateFrom.getFullYear().toString() +
                                   (dateFrom.getMonth() + 1).toString().padStart(2, '0') +
                                   dateFrom.getDate().toString().padStart(2, '0'));
        params.push(dateFromInt);
      }

      if (filters.date_to) {
        sql += " AND xpo.DemandBeginDate <= ?";
        // 将日期转换为YYYYMMDD格式的整数进行比较
        const dateTo = new Date(filters.date_to);
        const dateToInt = parseInt(dateTo.getFullYear().toString() +
                                 (dateTo.getMonth() + 1).toString().padStart(2, '0') +
                                 dateTo.getDate().toString().padStart(2, '0'));
        params.push(dateToInt);
      }

      sql += ` ORDER BY xpo.DemandBeginDate DESC`;

      const [rows] = await db.execute(sql, params);
      return rows;

    } catch (error) {
      console.warn('原始表查询失败，尝试使用备用表:', error.message);

      // 使用备用表
      let backupSql = `
        SELECT
          bt.task_number,
          bt.project_id,
          bt.construction_unit_id,
          bt.construction_unit,
          bt.strength_grade,
          bt.part_name,
          bt.scheduled_time,
          bt.supply_status,
          bt.actual_time,
          bt.feedback_user,
          bt.status,
          bt.project_name,
          0 as feedback_count
        FROM dbo.backup_tasks bt
        LEFT JOIN dbo.backup_projects bp ON bt.project_id = bp.id
        WHERE bp.company_id = ?
      `;
      const backupParams = [companyId];

      // 添加筛选条件
      if (filters.supply_status) {
        backupSql += " AND bt.supply_status = ?";
        backupParams.push(filters.supply_status);
      }

      if (filters.keyword) {
        backupSql += " AND (bt.task_number LIKE ? OR bt.part_name LIKE ?)";
        backupParams.push(`%${filters.keyword}%`, `%${filters.keyword}%`);
      }

      if (filters.date_from) {
        backupSql += " AND CAST(bt.scheduled_time AS DATE) >= ?";
        backupParams.push(filters.date_from);
      }

      if (filters.date_to) {
        backupSql += " AND CAST(bt.scheduled_time AS DATE) <= ?";
        backupParams.push(filters.date_to);
      }

      backupSql += ` ORDER BY bt.scheduled_time DESC`;

      const [backupRows] = await db.execute(backupSql, backupParams);
      return backupRows;
    }
  }

  // 获取工程的任务单列表（从新数据库X_ppProduceOrder表获取）
  static async getByProjectId(projectId, filters = {}) {
    let sql = `
      SELECT
        xpo.BillNo as id,
        xpo.BillNo as task_number,
        xpo.ProjectId as project_id,
        xpo.BizPartnerId as construction_unit_id,
        CONCAT(
          ISNULL(cmg.MaterialName, ''),
          CASE WHEN cmg.MaterialName IS NOT NULL AND xbi.ImperviousName IS NOT NULL THEN ' ' ELSE '' END,
          ISNULL(xbi.ImperviousName, ''),
          CASE WHEN (cmg.MaterialName IS NOT NULL OR xbi.ImperviousName IS NOT NULL) AND xbf.FolderName IS NOT NULL THEN ' ' ELSE '' END,
          ISNULL(xbf.FolderName, '')
        ) as strength_grade,
        xpo.X_JZPart as part_name,
        CASE
          WHEN xpo.DemandBeginDate IS NOT NULL AND xpo.DemandBeginDate > 0
          THEN CONVERT(DATETIME, CAST(xpo.DemandBeginDate AS VARCHAR(8)), 112)
          ELSE NULL
        END as scheduled_time,
        ISNULL(xpo.X_SupplyState, 0) as supply_status,
        NULL as actual_time,
        NULL as feedback_user,
        1 as status,
        cp.ProjectName as project_name,
        ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,
        cp.ProjectId as project_code,
        ISNULL(feedback_stats.feedback_count, 0) as feedback_count
      FROM dbo.X_ppProduceOrder xpo
      LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId
      LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId
      LEFT JOIN (
        SELECT
          f.TaskNumber,
          COUNT(DISTINCT f.Id) as feedback_count
        FROM dbo.CU_feedbacks f
        WHERE f.Status = 1
        GROUP BY f.TaskNumber
      ) feedback_stats ON xpo.BillNo = feedback_stats.TaskNumber
      WHERE xpo.ProjectId = ?
    `;
    const params = [projectId];

    // 添加筛选条件
    if (filters.supply_status !== undefined && filters.supply_status !== '') {
      // 支持数字状态值和字符串状态值
      let statusValue = filters.supply_status;
      if (typeof statusValue === 'string') {
        // 将字符串状态转换为数字状态
        const statusMap = {
          'pending': 0,
          'supplying': 1,
          'completed': 2,
          'cancelled': 3
        };
        statusValue = statusMap[statusValue] !== undefined ? statusMap[statusValue] : parseInt(statusValue);
      }
      sql += " AND xpo.X_SupplyState = ?";
      params.push(statusValue);
    }

    if (filters.keyword) {
      sql += " AND (xpo.BillNo LIKE ? OR xpo.X_JZPart LIKE ?)";
      params.push(`%${filters.keyword}%`, `%${filters.keyword}%`);
    }

    if (filters.date_from) {
      sql += " AND xpo.DemandBeginDate >= ?";
      // 将日期转换为YYYYMMDD格式的整数进行比较
      const dateFrom = new Date(filters.date_from);
      const dateFromInt = parseInt(dateFrom.getFullYear().toString() +
                                 (dateFrom.getMonth() + 1).toString().padStart(2, '0') +
                                 dateFrom.getDate().toString().padStart(2, '0'));
      params.push(dateFromInt);
    }

    if (filters.date_to) {
      sql += " AND xpo.DemandBeginDate <= ?";
      // 将日期转换为YYYYMMDD格式的整数进行比较
      const dateTo = new Date(filters.date_to);
      const dateToInt = parseInt(dateTo.getFullYear().toString() +
                               (dateTo.getMonth() + 1).toString().padStart(2, '0') +
                               dateTo.getDate().toString().padStart(2, '0'));
      params.push(dateToInt);
    }

    sql += ` ORDER BY xpo.DemandBeginDate DESC`;

    const [rows] = await db.execute(sql, params);

    // 添加调试日志 - 检查前3个任务的scheduled_time
    if (rows.length > 0) {
      console.log('=== Task.getByProjectId 调试信息 ===');
      rows.slice(0, 3).forEach((row, index) => {
        console.log(`任务 ${index + 1}: ${row.task_number}`);
        console.log(`  scheduled_time: ${row.scheduled_time}`);
        console.log(`  scheduled_time类型: ${typeof row.scheduled_time}`);
        console.log(`  scheduled_time构造函数: ${row.scheduled_time?.constructor?.name}`);
        if (row.scheduled_time) {
          console.log(`  getTime(): ${row.scheduled_time.getTime()}`);
        }
      });
    }

    return rows;
  }

  // 更新任务单信息（注意：新数据库中不支持更新任务单，只能从现有的X_ppProduceOrder表中读取）
  static async update(id, taskData) {
    // 在新的业务系统中，任务单数据来自X_ppProduceOrder表，不支持更新操作
    throw new Error('新数据库中不支持更新任务单，任务单数据来自X_ppProduceOrder表');
  }



  // 获取任务单的现场信息反馈记录
  static async getFeedbacks(taskNumber) {
    try {
      // 检查feedbacks表是否存在
      const [tableCheck] = await db.execute(`
        SELECT COUNT(*) as table_exists
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_NAME = 'feedbacks' AND TABLE_CATALOG = 'NBSTEST'
      `);

      if (tableCheck[0].table_exists === 0) {
        // feedbacks表不存在，返回空数组
        console.log('⚠️ feedbacks表不存在，返回空反馈记录');
        return [];
      }

      const [rows] = await db.execute(
        `
        SELECT f.id, f.task_number, f.feedback_user_id, f.feedback_time,
               CAST(f.notes AS NVARCHAR(MAX)) as notes, f.category,
               f.status,
               cgp.PersonName as feedback_user_name,
               0 as media_count
        FROM dbo.feedbacks f
        LEFT JOIN dbo.comPerson cp ON f.feedback_user_id = cp.PersonId
        LEFT JOIN dbo.comGroupPerson cgp ON cp.PersonId = cgp.PersonId
        WHERE f.task_number = ? AND f.status = 1
        ORDER BY f.feedback_time DESC
      `,
        [taskNumber]
      );
      return rows;
    } catch (error) {
      console.error('获取反馈记录失败:', error.message);
      // 如果查询失败，返回空数组
      return [];
    }
  }

  // 检查任务单号是否存在（从新数据库X_ppProduceOrder表检查）
  static async checkTaskNumberExists(projectId, taskNumber, excludeId = null) {
    try {
      const sql = `
        SELECT xpo.BillNo
        FROM dbo.X_ppProduceOrder xpo
        WHERE xpo.ProjectId = ? AND xpo.BillNo = ?
      `;
      const params = [projectId, taskNumber];

      const [rows] = await db.execute(sql, params);
      return rows.length > 0;
    } catch (error) {
      console.error('检查任务单号是否存在失败:', error);
      throw error;
    }
  }
}

module.exports = Task;
